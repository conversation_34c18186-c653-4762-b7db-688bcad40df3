{"format": 1, "restore": {"/Users/<USER>/git/hello-world-c-sharp/hello-world-c-sharp.csproj": {}}, "projects": {"/Users/<USER>/git/hello-world-c-sharp/hello-world-c-sharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/git/hello-world-c-sharp/hello-world-c-sharp.csproj", "projectName": "hello-world-c-sharp", "projectPath": "/Users/<USER>/git/hello-world-c-sharp/hello-world-c-sharp.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/git/hello-world-c-sharp/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}